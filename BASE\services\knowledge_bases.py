class QdrantKnowledgeBase:
    @staticmethod
    def _get_kbdb() -> Collection:
        return Database()["knowledge_bases"]

    @staticmethod
    def exists(name: str) -> bool:
        """Check if a knowledge base with the given name exists."""
        return QdrantKnowledgeBase._get_kbdb().find_one({"name": name}) is not None

    @staticmethod
    def exists_id(kbid: str) -> bool:
        """Check if a knowledge base with the given kbid exists"""
        return QdrantKnowledgeBase._get_kbdb().find({"id": kbid}) is not None

    @staticmethod
    def exists_by_path(path: str) -> Optional["QdrantKnowledgeBase"]:
        """Check if a knowledge base with the given path exists and return it."""
        # Normalize path for consistent comparison
        normalized_path = str(Path(path).resolve())

        # Search for any KB type with matching path in metadata
        kb_record = QdrantKnowledgeBase._get_kbdb().find_one({
            "metadata.path": normalized_path
        })

        if kb_record:
            return QdrantKnowledgeBase(**kb_record)
        return None

    @staticmethod
    def exists_auto_indexed_by_path(path: str) -> Optional["QdrantKnowledgeBase"]:
        """Check if an auto-indexed knowledge base with the given path exists."""
        # Normalize path for consistent comparison
        normalized_path = str(Path(path).resolve())

        # Search for auto-indexed KBs with matching path
        kb_record = QdrantKnowledgeBase._get_kbdb().find_one({
            "metadata.path": normalized_path,
            "isAutoIndexed": True
        })

        if kb_record:
            return QdrantKnowledgeBase(**kb_record)
        return None