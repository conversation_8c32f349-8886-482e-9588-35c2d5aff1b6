from ipc import IPC
from BASE.utils.platform_detector import PlatformDetector
from BASE.services.qdrant import get_qdrant_client

"""
We create an IPC instance for shared memory communication.
Rather than using events (for now), we use a shared pool to manage states.
We also initialize the global Qdrant client for vector database operations.
"""
ipc_ = IPC.connect()
ipc_.set("platform", PlatformDetector.get_os_name())
ipc_.set("ollama_running", False)
ipc_.set("internet_connected", False)
ipc_.set("internet_available", False)

# Initialize global Qdrant client
qdrant_client = get_qdrant_client()
ipc_.set("qdrant_initialized", True)