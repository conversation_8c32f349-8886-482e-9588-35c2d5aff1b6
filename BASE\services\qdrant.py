from qdrant_client import AsyncQdrantClient
from BASE.utils.path_selector import PathSelector

# Global Qdrant instance
_qdrant_client = None

def get_qdrant_client() -> AsyncQdrantClient:
    """
    Get the global Qdrant client instance.
    Creates a new instance if one doesn't exist.
    """
    global _qdrant_client

    if _qdrant_client is None:
        _qdrant_client = create_qdrant_client()

    return _qdrant_client

def create_qdrant_client() -> AsyncQdrantClient:
    """
    Create a new Qdrant client instance.
    Uses local file-based storage in the .neocortex directory.
    """
    db_path = PathSelector.get_qdrant_db_path()

    # Create Qdrant client with local file storage
    client = AsyncQdrantClient(path=str(db_path))

    return client

def reset_qdrant_client():
    """
    Reset the global Qdrant client instance.
    Useful for testing or when configuration changes.
    """
    global _qdrant_client
    _qdrant_client = None